<script lang="ts">
  import * as Form from "$lib/components/ui/form"
  import * as Card from "$lib/components/ui/card"
  import { superForm } from "sveltekit-superforms"
  import { zodClient } from "sveltekit-superforms/adapters"
  import { emailSchema } from "$lib/schemas"
  import { Input } from "$lib/components/ui/input"
  import { Button } from "$lib/components/ui/button"

  let { data } = $props()

  const form = superForm(data.form, {
    validators: zodClient(emailSchema),
  })

  const { form: formData, enhance, delayed, errors, constraints } = form
</script>

<svelte:head>
  <title>Sign In with Email Code</title>
</svelte:head>

<Card.Root class="mt-6">
  <Card.Header>
    <Card.Title class="text-2xl font-bold text-center">Sign In</Card.Title>
    <Card.Description>
      Enter your email address and we'll send you a 6-digit code to sign in
    </Card.Description>
  </Card.Header>
  <Card.Content>
    <form method="post" use:enhance class="grid gap-4">
      <Form.Field {form} name="email">
        <Form.Control let:attrs>
          <Form.Label>Email address</Form.Label>
          <Input
            bind:value={$formData.email}
            {...attrs}
            {...$constraints.email}
            type="email"
            placeholder="Enter your email"
          />
        </Form.Control>
        <Form.FieldErrors />
      </Form.Field>

      {#if $errors._errors}
        <p class="text-destructive text-sm font-bold mt-1">
          {$errors._errors[0]}
        </p>
      {/if}

      <Button type="submit" disabled={$delayed} class="w-full">
        {#if $delayed}
          Sending code...
        {:else}
          Send verification code
        {/if}
      </Button>
    </form>
  </Card.Content>
</Card.Root>

<div class="text-l text-primary mt-4 text-center">
  Don't have an account? <a class="underline" href="/login/passwordless_signup">Sign up</a>.
</div>
<div class="text-l text-primary mt-2 text-center">
  Prefer password signin? <a class="underline" href="/login/sign_in">Sign in with password</a>.
</div>
