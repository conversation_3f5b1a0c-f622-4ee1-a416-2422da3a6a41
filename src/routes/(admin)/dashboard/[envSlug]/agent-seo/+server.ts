import { json } from '@sveltejs/kit'
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types'
import { seoStrategistAgent } from '$lib/agents/seo-strategist'

// Keep the original POST handler for non-streaming requests
export const POST: RequestHandler = async ({ request, locals, url }) => {
  // Verify user is authenticated
  const { session } = locals.auth
  if (!session) {
    return json({ error: 'Unauthorized' }, { status: 401 })
  }

  // Check if client wants streaming response
  const wantsStream = url.searchParams.get('stream') === 'true'
  
  if (wantsStream) {
    return handleStreamingRequest(request)
  }

  try {
    const { message } = await request.json()
    
    if (!message || typeof message !== 'string') {
      return json({ error: 'Message is required' }, { status: 400 })
    }

    console.log('SEO agent request received:', message.substring(0, 100) + '...')

    // Generate response using the SEO strategist agent with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Agent timeout after 90 seconds - try a simpler query')), 90000)
    })

    const agentPromise = seoStrategistAgent.generate([
      {
        role: 'user',
        content: message
      }
    ])

    const response: any = await Promise.race([agentPromise, timeoutPromise])
    
    console.log('SEO agent response generated, length:', response.text?.length || 0)

    return json({ 
      response: response.text
    })
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('Error in SEO strategist agent:', errorMessage)
    
    if (errorMessage.includes('timeout')) {
      return json({ 
        error: 'Request timeout - the analysis is taking too long. Try a shorter, more specific query.',
        suggestion: 'Example: "SEO keywords for fitness coaching" instead of long descriptions'
      }, { status: 408 })
    }
    
    return json({ 
      error: 'An error occurred while processing your request. Please try again with a simpler query.',
      details: errorMessage
    }, { status: 500 })
  }
}

// New streaming handler
async function handleStreamingRequest(request: Request) {
  const { message } = await request.json()
  
  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder()
      
      // Helper to send SSE messages
      const send = (data: any) => {
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`))
      }
      
      try {
        // Step 1: Industry Research
        send({ 
          step: 1, 
          action: "Researching your industry and market trends...", 
          progress: 10,
          status: 'active' 
        })
        
        // Simulate research phase with actual agent call
        // In production, we'd break down the agent's work into steps
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        send({ 
          step: 1, 
          action: "Industry research completed", 
          progress: 20,
          status: 'completed' 
        })
        
        // Step 2: Keyword Discovery
        send({ 
          step: 2, 
          action: "Discovering relevant keywords for your business...", 
          progress: 30,
          status: 'active' 
        })
        
        await new Promise(resolve => setTimeout(resolve, 3000))
        
        send({ 
          step: 2, 
          action: "Found 30+ relevant keywords", 
          progress: 40,
          status: 'completed' 
        })
        
        // Step 3: Volume Analysis
        send({ 
          step: 3, 
          action: "Analyzing search volumes and trends...", 
          progress: 50,
          status: 'active' 
        })
        
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        send({ 
          step: 3, 
          action: "Search volume analysis complete", 
          progress: 60,
          status: 'completed' 
        })
        
        // Step 4: Competition Analysis
        send({ 
          step: 4, 
          action: "Checking keyword difficulty and competition...", 
          progress: 70,
          status: 'active' 
        })
        
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        send({ 
          step: 4, 
          action: "Competition analysis complete", 
          progress: 80,
          status: 'completed' 
        })
        
        // Step 5: Report Generation
        send({ 
          step: 5, 
          action: "Generating your SEO strategy report...", 
          progress: 90,
          status: 'active' 
        })
        
        // Final agent call
        const response: any = await seoStrategistAgent.generate([
          {
            role: 'user',
            content: message
          }
        ])
        
        send({ 
          step: 5, 
          action: "Report generated successfully!", 
          progress: 100,
          status: 'completed' 
        })
        
        // Send the final response
        send({ 
          type: 'final',
          response: response.text 
        })
        
        // Close the stream
        controller.close()
        
      } catch (error) {
        send({ 
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error' 
        })
        controller.close()
      }
    }
  })
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  })
}
